# 部署说明

## Umami 分析集成

本项目已成功集成 Umami 分析服务：

- **Umami 服务器**: https://umami.lacs.cc/script.js
- **网站 ID**: a4e8c20f-d2e8-4b10-bdf5-2d52c389fd45

### 配置位置

1. **Analytics 组件**: `src/components/common/Analytics.astro`
   - 添加了 Umami 脚本支持
   - 使用 `is:inline` 指令确保正确加载

2. **配置文件**: `src/config.yaml`
   - 在 `analytics.vendors` 下添加了 `umami` 配置
   - 包含 `websiteId` 和 `src` 参数

### 验证

构建后的 HTML 文件中已包含以下脚本：
```html
<script src="https://umami.lacs.cc/script.js" data-website-id="a4e8c20f-d2e8-4b10-bdf5-2d52c389fd45" defer="defer"></script>
```

## Vercel 部署

项目已准备好部署到 Vercel：

### 配置文件

- **vercel.json**: 已配置正确的构建命令和输出目录
- **构建命令**: `npm run build`
- **输出目录**: `dist`

### 缓存策略

已为以下文件类型配置了长期缓存：
- JavaScript 文件 (1年)
- CSS 文件 (1年)
- 图片文件 (1年)
- 字体文件 (1年)

### 部署步骤

1. 确保代码已推送到 Git 仓库
2. 在 Vercel 中导入项目
3. Vercel 会自动检测 Astro 项目并使用正确的设置
4. 部署完成后，Umami 分析将自动开始收集数据

### 构建验证

项目已成功构建，生成了以下关键文件：
- `dist/index.html` - 主页
- `dist/about/index.html` - 关于页面
- `dist/_astro/` - 静态资源
- 所有页面都包含 Umami 脚本

## 注意事项

1. 构建过程中移除了 startup 页面的远程图片引用以避免网络超时
2. 所有页面都正确包含了 Umami 分析脚本
3. Vercel 配置已优化，包含适当的缓存头
